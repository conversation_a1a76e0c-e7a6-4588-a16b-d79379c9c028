import { CurrencyPipe, formatDate } from '@angular/common';
import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';
import { NgbModal, ModalDismissReasons, NgbModalOptions, NgbActiveModal, } from '@ng-bootstrap/ng-bootstrap';
import { UserService } from '../../../core/services/user.service';
import { NotificationService } from '../../../core/services/notification.service';
import { User } from '../../../core/models/user';
import { Faq } from '../../../core/models/faq';
import { FaqService } from '../../../core/services/faq.service';

@Component({
  selector: 'app-help',
  host: { ngSkipHydration: 'true' },
  templateUrl: './help.component.html',
  styleUrls: ['./help.component.scss'],
})
export class HelpComponent implements OnInit, OnDestroy {

  subs = new Subscription();

  user: User;
  faqs: Faq[] = [];

  expandableId: string;
  modalOptions: NgbModalOptions;
  previousPage: any;

  isLoading = false; error = null;
  isModelLoading = false; Modelerror = null;
  isModelPaymentLoading = false; ModelPaymenterror = null;

  options = { disabled: 0, nopaginate: 1, query: null, page: 'go-grubz', customer_id: null };

  constructor(
    public userService: UserService,
    public faqService: FaqService,
    private router: Router,
    private modalService: NgbModal,
    // public activeModal: NgbActiveModal,
    // private currencyPipe: CurrencyPipe,
    private notificationService: NotificationService
  ) { }

  ngOnInit(): void {
    this.user = JSON.parse(this.userService.getUser());
    this.options.customer_id = this.user?.id;
    if (!this.user.id) {
      this.router.navigateByUrl('/');
    }
    this.fetchFaqs();
  }

  fetchFaqs() {
    this.isLoading = true; this.error = null;

    this.subs.add(
      this.faqService.home(this.options)
        .pipe(finalize(() => (this.isLoading = false)))
        .subscribe(
          (res) => {
            this.faqs = res;
            this.expandableId = this.faqs[0].id;
          },
          (err) => {
            this.faqs = [];
          }
        )
    );
  }

  clickOnAccordion(faqId) {
    if (faqId == this.expandableId) {
      faqId = null;
    }
    this.expandableId = faqId;
  }

  applyFilters() {
    this.router.navigate([], { queryParams: this.options });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
