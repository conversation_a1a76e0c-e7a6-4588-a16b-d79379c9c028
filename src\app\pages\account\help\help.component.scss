.help-support-box{
    padding: 30px ;
    border-radius: 15px;
    text-align: center;
    background-color: #FFFFFF;
    transition: all 0.5s;
    box-shadow: 0px 0px 22.5px 0.75px #0000001A;
    margin-bottom: 40px;
}
.help-support-box h6{
    font-family:'Fredoka One';
    font-size: 30px;
    font-weight: 400;
    line-height: 40px;
    margin-bottom: 30px;
}
.help-support-box p{
    font-family: 'Visby CF';
    font-size: 18px;
    font-weight: 600;
    line-height: 22px; 
    margin-bottom: 30px;   
}
.help-support-box .btn{
    width:185px;
    font-size: 18px;
}
.help-support-box:hover{
    background-color: #FC353A;
}
.help-support-box:hover p,
.help-support-box:hover h6{
    color:#FFFFFF;
}
.help-support-box:hover button.btn{
    color:#FC353A !important;
    border-color:#FFFFFF !important;
    background-color: #FFFFFF !important;
}
.help-support-box button.btn:hover{
    border-color: #fff !important;
    color: #FFFFFF !important;
    background-color: transparent !important;
}

.main-heading {
    margin-bottom: 10px;
}

.main-heading h6 {
    font-family: 'Visby CF';
    font-weight: 700;
    font-size: 30px;
    margin-bottom: 0;
}

.accordion-item {
    margin: 40px 0;
    border: 0;
    box-shadow: 0px 0px 10px 3px #0000001A;
    border-radius: 15px;
}

.accordion-button:after {
    transition: all 0.5s;
}

.accordion-header button.accordion-button {
    font-family: 'Visby CF';
    font-size: 24px;
    color: #000000;
    display: inline-block;
    width: 100%;
    text-decoration: none;
    font-weight: 700;
    position: relative;
    padding:13px 15px 13px 90px;
    box-shadow: none;
    color: #FFFFFF;
    background-color: #ea3323;
    border-radius: 15px;
    cursor: pointer;
}

.accordion-header button.accordion-button.collapsed {
    color: #202020;
    background-color: #FFFFFF;
}
.accordion-header button.accordion-button::before {
    width: 35px;
    height: 35px;
    text-align: center;
    font-size: 24px;
    position: absolute;
    left: 25px;
    top: 13px;
    content: "\f068";
    font-family: 'fontawesome';    
    transition: all 0.5s;
    color: #FFFFFF;
    
}
.accordion-header button.accordion-button.collapsed::before {
    content: "\2b";
    color: #202020;
}

.accordion-body {
    padding:25px 30px 25px 90px;
}

.accordion-body p {
    font-size: 20px;
}

@media screen and (max-width:1500px) {
    .help-support-box{
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 30px;
    }
    .help-support-box h6 {
        font-size: 25px;
        margin-bottom: 25px;
    }
    .help-support-box p {
        font-size: 14px;
        line-height: 18px;
        margin-bottom: 30px;
    }
    .help-support-box .btn {
        font-size: 13px;
        width: 132px;        
        padding: 3px 10px;
    }
    .main-heading h6 {
        font-size: 24px;
    }
    .accordion-item{
        margin: 30px 0;
        border-radius: 12px;
    }
    .accordion-header button.accordion-button {
        font-size: 20px;
        padding: 11px 15px 11px 70px;
        border-radius: 12px;
    }
    .accordion-header button.accordion-button:before {
        font-size: 20px;        
        top: 11px;
        left: 20px;        
        width: 30px;
        height: 30px;
    }
    .accordion-body{
        padding: 20px 20px 20px 70px;
    }
    .accordion-body p {
        font-size: 18px;
    }

}

@media screen and (max-width:1199px) {
    .help-support-box{
        padding: 15px;
    }    
    .help-support-box h6 {
        font-size: 22px;
        margin-bottom: 15px;
    }
    .help-support-box p {
        font-size: 13px;
        margin-bottom: 20px;
    }
    .accordion-body p {
        font-size: 16px;
    }

}

@media screen and (max-width:991px) {
    .help-support-box h6 {
        line-height: 30px;
    }

}

@media screen and (max-width:767px) {
    .accordion-item{
        margin: 20px 0;
    }
    .accordion-header button.accordion-button {
        font-size: 18px;
        padding: 10px 10px 10px 60px;
    }    
    .accordion-body{
        padding: 15px 15px 15px 60px;
    }
    .accordion-header button.accordion-button:before {
        font-size: 16px;
        top: 12px;
        left: 15px;
    }

}