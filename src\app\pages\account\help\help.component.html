<div class="row">
  <div class="col-md-6">
    <div class="help-support-box">
      <h6>
        Go Grubz Customer Support
      </h6>
      <p>
        Need help with an order, your account or
        other general enquiries? Contact Go Grubz
        support for quick assistance and a
        delightful ordering experience!
      </p>
      <button class="btn" href="#">
        Customer Support
      </button>
    </div>
  </div>
  <div class="col-md-6">
    <div class="help-support-box">
      <h6>
        Merchant Partner Support
      </h6>
      <p>
        For our valued merchant partners already
        on board with Go Grubz, our dedicated
        account support is here to assist you every
        step of the way.
      </p>
      <button class="btn" href="#">
        Partner Support
      </button>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-sm-12">
    <div class="main-heading text-center pt-2">
      <h6>Frequently Asked Questions</h6>
    </div>
  </div>
</div>
<div class="row">
  <div class="col-sm-12">
    <div class="accordion" id="faq-accordion">

      <div class="accordion-item" *ngFor="let faq of faqs;">
        <div class="accordion-header" [id]="'heading'+faq.id">
          <button class="accordion-button" [class.collapsed]="expandableId != faq.id" data-bs-toggle="collapse"
            [data-bs-target]="'#faq-accordion-'+faq.id" (click)="clickOnAccordion(faq.id)">
            {{faq.question}}
          </button>
        </div>
        <div [id]="'faq-accordion-'+faq.id" class="accordion-collapse collapse"
          [class.collapse]="expandableId != faq.id" [aria-labelledby]="'heading'+faq.id"
          data-bs-parent="#faq-accordion">
          <div class="accordion-body">
            <p [innerHTML]="faq.answer"></p>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>